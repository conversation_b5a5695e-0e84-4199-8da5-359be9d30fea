import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:maria_filo_app_flutter/widgets/widgets.dart';
import 'package:soma_ui/soma_ui.dart';
import 'package:soma_ui/utils/clock_utils.dart';

class MariaFiloClock extends StatefulWidget {
  final Widget? titleWidget;
  final Widget? hoursWidget;
  final String? title;
  final String? coupon;
  final String? buttonText;
  final Color? backgroundColor;
  final Color? textColor;
  final VoidCallback? onTapButton;
  final Duration? remainingTime;
  final BannerNameGA4? bannerNameGA4;
  final String? subtitle;
  final String? navigateTo;

  const MariaFiloClock({
    super.key,
    required this.title,
    required this.coupon,
    required this.backgroundColor,
    required this.textColor,
    required this.onTapButton,
    this.buttonText,
    this.hoursWidget,
    this.titleWidget,
    this.remainingTime,
    this.bannerNameGA4,
    this.subtitle = 'Em peças de Inverno 25.',
    this.navigateTo = 'urlexiste',
  });

  factory MariaFiloClock.fromClockHeader(
    ClockHeader clock, {
    VoidCallback? onTapButton,
    Duration? remainingTime,
  }) {
    return MariaFiloClock(
      title: clock.title,
      coupon: clock.coupon,
      buttonText: clock.buttonText,
      backgroundColor: clock.backgroundColor,
      textColor: clock.textColor,
      onTapButton: onTapButton,
      remainingTime: remainingTime,
      bannerNameGA4: clock.bannerNameGA4,
    );
  }

  @override
  State<StatefulWidget> createState() => _MariaFiloClockState();
}

class _MariaFiloClockState extends State<MariaFiloClock>
    with
        DesignTokensStateMixin,
        CouponPromotionMixin,
        AnalyticsEventDispatcherStateMixin {
  @override
  void initState() {
    super.initState();
    scheduleMicrotask(() => _logPromotionEvents(isViewPromotion: true));
  }

  void _logPromotionEvents({bool isViewPromotion = false}) {
    final inheritedMetadata = AnalyticsMetadataProvider.metadataOf(context);
    String? screenName = inheritedMetadata?['screen_name'].toLowerCase();
    if (isViewPromotion) {
      try {
        dispatchViewPromotionEvent(
          context,
          screenClass: 'reloginho',
          screenName: screenName,
          promotionName: widget.bannerNameGA4?.promotionName,
          creativeName: widget.bannerNameGA4?.creativeName,
          creativeSlot: 'Banner sem imagem',
          promotionId: "reloginho-$screenName",
        );
      } catch (e) {
        debugPrint("Error on dispatchViewPromotionEvent: $e");
      }
    } else {
      try {
        dispatchSelectPromotionEvent(
          screenClass: 'reloginho',
          screenName: screenName,
          promotionName: widget.bannerNameGA4?.promotionName,
          creativeName: widget.bannerNameGA4?.creativeName,
          creativeSlot: 'Banner sem imagem',
          promotionId: "reloginho-$screenName",
        );
      } catch (e) {
        debugPrint("Error on dispatchSelectPromotionEvent: $e");
      }
    }
  }

  Widget _titleWidget() {
    return widget.title != null && widget.title!.isNotEmpty
        ? Flexible(
            child: TextButton(
              style: TextButton.styleFrom(
                textStyle: typeStyles.subtitle,
                padding: EdgeInsets.zero,
                minimumSize: Size.zero,
                // tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                foregroundColor: widget.textColor,
              ),
              onPressed: widget.onTapButton,
              child: Text(
                widget.title!,
                style: typeStyles.body.copyWith(
                  color: widget.textColor,
                  fontSize: tokens.typography.fontSizes.xus,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          )
        : const SizedBox.shrink();
  }

  Widget _subtitleWidget() {
    // Se tem cupom, mostra o cupom com formatação especial
    if (hasCoupon && widget.coupon != null && widget.coupon!.isNotEmpty) {
      return Flexible(
        child: RichText(
          text: TextSpan(
            text: 'CUPOM: ',
            style: typeStyles.body.copyWith(
              color: widget.textColor,
              fontSize: tokens.typography.fontSizes.xus,
              fontWeight: FontWeight.w300,
            ),
            children: [
              TextSpan(
                text: widget.coupon,
                style: typeStyles.body.copyWith(
                  color: widget.textColor,
                  fontSize: tokens.typography.fontSizes.xus,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Se não tem cupom mas tem subtitle, mostra o subtitle
    if (widget.subtitle != null && widget.subtitle!.isNotEmpty) {
      return Flexible(
        child: Text(
          widget.subtitle!,
          style: typeStyles.body.copyWith(
            color: widget.textColor,
            fontSize: tokens.typography.fontSizes.xus,
            fontWeight: FontWeight.w300,
          ),
        ),
      );
    }

    // Caso padrão: retorna widget vazio
    return const SizedBox.shrink();
  }

  Widget _buttonRule() {
    return SMButton.link(
      onPressed: _showRulesModal,
      // size: ButtonSize.extraSmall,
      style: const SMButtonStyle(
        padding: EdgeInsets.zero,
      ),
      child: Text(
        'Confira as regras',
        style: typeStyles.body.copyWith(
          color: widget.textColor,
          fontWeight: FontWeight.bold,
          fontSize: tokens.typography.fontSizes.xxus,
          decoration: TextDecoration.underline,
          // height: 2,
        ),
      ),
    );
  }

  void _showRulesModal() {
    Get.bottomSheet(
      Container(
        height: MediaQuery.of(Get.context!).size.height * 0.7, // 70% da altura
        decoration: BoxDecoration(
          color: tokens.colors.neutral.pure1,
          // borderRadius: BorderRadius.only(
          //   topLeft: Radius.circular(16),
          //   topRight: Radius.circular(16),
          // ),
        ),
        child: Column(
          children: [
            // Header com título e botão fechar
            Padding(
              padding: EdgeInsets.all(spacingInset.lg),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Regras da promoção',
                    style: typeStyles.headlineSm,
                  ),
                  IconButton(
                    onPressed: () => navigateBack(),
                    icon: Icon(tokens.icons.close, size: 24),
                  ),
                ],
              ),
            ),
            // Conteúdo
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.symmetric(horizontal: spacingInset.lg),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Desconto não cumulativo válido em peças selecionadas. Na compra de 3, a de menor valor é grátis. Os valores serão redistribuídos entre as peças por questões fiscais com o cupom LEVE3, por tempo limitado exclusivo no app.',
                      style: typeStyles.body.copyWith(
                        color: tokens.colors.typography.pure2,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    SizedBox(height: spacingStack.lg),
                    _buildCouponContainer(),
                    SizedBox(height: spacingStack.lg),
                    SMButton.primary(
                      child: Text('VER PRODUTOS'),
                      onPressed: () {
                        navigateBack();
                        // Navegação para produtos
                      },
                      expanded: true,
                    )
                  ],
                ),
              ),
            ),
            // Botão
          ],
        ),
      ),
      isScrollControlled: false, // ✅ Não ocupa tela toda
      enableDrag: true,
      isDismissible: true,
    );
  }

  Widget _buildCouponContainer() {
    return InkWell(
      onTap: () => _copyCoupon(),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(spacingInset.md),
        decoration: BoxDecoration(
          border: DashedBorder(color: tokens.colors.neutral.medium1),
          // borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'CUPOM LEVE3',
                  style: typeStyles.body.copyWith(
                    color: tokens.colors.typography.pure2,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                SizedBox(width: spacingInline.xs),
                Icon(
                  tokens.icons.copy,
                  size: 16,
                  color: tokens.colors.typography.pure2,
                ),
              ],
            ),
            SizedBox(height: spacingStack.xxs),
            Text(
              'Copiar cupom',
              style: typeStyles.bodyCaption.copyWith(
                color: tokens.colors.typography.light2,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _copyCoupon() {
    print('🔥 Copiando cupom LEVE3'); // Debug
    Clipboard.setData(const ClipboardData(text: 'LEVE3'));

    // Feedback visual
    Get.snackbar(
      'Cupom copiado!',
      'LEVE3 foi copiado para a área de transferência',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: tokens.colors.feedback.pureSuccess,
      colorText: tokens.colors.neutral.pure1,
      duration: const Duration(seconds: 2),
      margin: EdgeInsets.all(spacingInset.md),
      borderRadius: 8,
    );
  }

  Widget _buttomWidget() {
    final foregroundColor = isButtonSelected
        ? tokens.colors.feedback.pureSuccess
        : widget.textColor;
    return SMButton.secondary(
      onPressed: () async {
        _logPromotionEvents();
        if (hasCoupon) {
          await onTapCoupon();
        } else {
          // Redireciona para o mesmo destino que _titleWidget
          if (widget.onTapButton != null) {
            widget.onTapButton!();
          }
        }
      },
      //size: ButtonSize.small,
      style: SMButtonStyle(
        backgroundColor: widget.backgroundColor,
        padding: const EdgeInsets.fromLTRB(0, 6, 0, 8),
        border: isButtonSelected
            ? DashedBorder(
                color: tokens.colors.feedback.pureSuccess,
              )
            : Border.all(
                color: widget.textColor ?? Colors.transparent,
                width: 1,
              ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        // mainAxisSize: 147,
        children: [
          Text(
            hasCoupon ? getClockButtonText() : 'CONFIRA',
            style: typeStyles.bodyCaption.copyWith(
              color: foregroundColor,
              fontWeight: FontWeight.bold,
              fontSize: tokens.typography.fontSizes.xxus,
              height: tokens.typography.lineHeight.us /
                  tokens.typography.fontSizes
                      .xxus, // line-height 16px / font-size 10px = 1.6
            ),
          )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    Duration remaingTime = widget.remainingTime ?? Duration.zero;

    return GestureDetector(
      onTap: widget.onTapButton,
      child: Container(
        width: MediaQuery.of(context).size.width,
        color: widget.backgroundColor,
        child: Padding(
          padding: EdgeInsets.symmetric(
              horizontal: spacingStack.sm, vertical: spacingStack.sm),
          child: hasButton // && hasCoupon
              ? Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Flexible(
                      flex: 4,
                      child: Padding(
                        padding: EdgeInsets.zero,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(height: 2),
                            _titleWidget(),
                            SizedBox(height: spacingStack.xs),
                            _subtitleWidget(),
                            SizedBox(height: spacingStack.xxs * 3),
                            _buttonRule(),
                          ],
                        ),
                      ),
                    ),
                    Flexible(
                      flex: 3,
                      child: Padding(
                        padding: EdgeInsets.zero,
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            ClockTime(
                              remainingTime: remaingTime,
                              textColor: widget.textColor,
                              hasIcon: false,
                            ),
                            SizedBox(height: spacingStack.xs),
                            _buttomWidget(),
                            SizedBox(height: spacingStack.xxs),
                          ],
                        ),
                      ),
                    ),
                  ],
                )
              : Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    if (widget.title != null && widget.title!.isNotEmpty)
                      _titleWidget(),
                    ClockTime(
                      remainingTime: remaingTime,
                      textColor: widget.textColor,
                    ),
                  ].separated(SizedBox(width: spacingStack.sm)),
                ),
        ),
      ),
    );
  }

  @override
  String? get coupon => widget.coupon;

  @override
  String? get buttonText => widget.buttonText;

  @override
  String get snackbarTextForEmptyBag =>
      'Ops! você precisa ter 1 ou mais produtos na sua Sacola';

  @override
  String get snackbarTextForCouponApplied =>
      'Cupom aplicado com sucesso em sua sacola!';
}

Widget Function(BuildContext, ClockHeaderCmsComponent, Duration)
    mariaFiloClockBuilder() {
  return (BuildContext context, ClockHeaderCmsComponent clockComponent,
          Duration remainingTime) =>
      MariaFiloClock.fromClockHeader(
        clockComponent.clock,
        remainingTime: remainingTime,
        onTapButton: () => ClockUtils.onTapButton(
          context,
          navigateTo: clockComponent.clock.navigateTo,
          type: clockComponent.componentType,
          componentIndex: 0,
          bannerNameGA4: clockComponent.clock.bannerNameGA4,
        ),
      );
}
